/**
 * Test the thumbnail URL fixing functionality
 */

// Simulate the validateStorageUrl function
function validateStorageUrl(url) {
  if (!url) return '';

  // Clean up any whitespace, newline characters, and URL encoding issues that might be in the URL
  // This fixes the %0A encoding issue found in the database
  let cleanedUrl = url.trim()
    .replace(/[\r\n\t]/g, '')  // Remove actual newline/tab characters
    .replace(/%0A/g, '')       // Remove URL-encoded newlines
    .replace(/%0D/g, '')       // Remove URL-encoded carriage returns
    .replace(/%09/g, '');      // Remove URL-encoded tabs

  // Check if it's already a valid URL (Supabase or Namecheap)
  if (cleanedUrl.includes('supabase.co/storage/v1/object/public/') ||
      cleanedUrl.includes('bluefilmx.com/media/') ||
      cleanedUrl.includes('premium34.web-hosting.com') ||
      cleanedUrl.startsWith('http://') ||
      cleanedUrl.startsWith('https://')) {
    return cleanedUrl;
  }

  // If it's a relative path for Supabase, construct the full URL
  if (cleanedUrl.startsWith('/storage/') || cleanedUrl.startsWith('storage/')) {
    const cleanPath = cleanedUrl.startsWith('/') ? cleanedUrl.slice(1) : cleanedUrl;
    return `https://vsnsglgyapexhwyfylic.supabase.co/${cleanPath}`;
  }

  // If it's a relative path for Namecheap media, construct the full URL
  if (cleanedUrl.startsWith('/media/') || cleanedUrl.startsWith('media/')) {
    const cleanPath = cleanedUrl.startsWith('/') ? cleanedUrl : `/${cleanedUrl}`;
    // Use the correct domain for your Namecheap hosting
    return `https://www.bluefilmx.com${cleanPath}`;
  }

  // Handle relative paths that might be missing the /media/ prefix
  if (!cleanedUrl.includes('://') && !cleanedUrl.startsWith('/')) {
    // Assume it's a media file and add the media path
    return `https://www.bluefilmx.com/media/${cleanedUrl}`;
  }

  return cleanedUrl;
}

// Test cases
const testUrls = [
  // Problematic URLs from the database
  'https://vsnsglgyapexhwyfylic.supabase.co%0A/storage/v1/object/public/thumbnails/c2157ddd-2f88-436a-8ae7-f2b828b30145/1749178343045-20250606_005527-collage.jpg',
  'https://vsnsglgyapexhwyfylic.supabase.co%0A/storage/v1/object/public/videos/c2157ddd-2f88-436a-8ae7-f2b828b30145/1749178321571-battalion_of_bad_bitches_0_.mp4',
  
  // URLs with different encoding issues
  'https://example.com%0D%0A/path/to/image.jpg',
  'https://example.com%09/path/to/image.jpg',
  
  // Normal URLs (should remain unchanged)
  'https://www.bluefilmx.com/media/thumbnails/image.jpg',
  'https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/image.jpg',
  
  // Relative paths
  '/media/thumbnails/image.jpg',
  'media/thumbnails/image.jpg',
  '/storage/v1/object/public/thumbnails/image.jpg',
  'storage/v1/object/public/thumbnails/image.jpg',
  
  // Edge cases
  '',
  null,
  undefined,
  'image.jpg'
];

console.log('🧪 Testing thumbnail URL fixing...\n');

testUrls.forEach((url, index) => {
  console.log(`Test ${index + 1}:`);
  console.log(`Input:  ${JSON.stringify(url)}`);
  
  try {
    const result = validateStorageUrl(url);
    console.log(`Output: ${JSON.stringify(result)}`);
    
    // Check if the result is a valid URL
    if (result) {
      try {
        new URL(result);
        console.log(`✅ Valid URL`);
      } catch (e) {
        console.log(`❌ Invalid URL format`);
      }
    } else {
      console.log(`ℹ️  Empty result`);
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }
  
  console.log('');
});

// Test the specific problematic URL from the API
console.log('🔍 Testing the specific problematic URL from the API...\n');

const problematicUrl = 'https://vsnsglgyapexhwyfylic.supabase.co%0A/storage/v1/object/public/thumbnails/c2157ddd-2f88-436a-8ae7-f2b828b30145/1749178343045-20250606_005527-collage.jpg';
const fixedUrl = validateStorageUrl(problematicUrl);

console.log('Original URL:', problematicUrl);
console.log('Fixed URL:   ', fixedUrl);
console.log('');

// Test if the fixed URL is accessible
async function testUrlAccessibility(url) {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return {
      accessible: response.ok,
      status: response.status,
      statusText: response.statusText
    };
  } catch (error) {
    return {
      accessible: false,
      error: error.message
    };
  }
}

// Test accessibility of the fixed URL
testUrlAccessibility(fixedUrl).then(result => {
  console.log('Accessibility test result:');
  console.log('Accessible:', result.accessible);
  if (result.accessible) {
    console.log('Status:', result.status, result.statusText);
    console.log('✅ Fixed URL is accessible!');
  } else {
    console.log('Error:', result.error || `${result.status} ${result.statusText}`);
    console.log('❌ Fixed URL is not accessible');
  }
}).catch(error => {
  console.log('❌ Error testing URL accessibility:', error.message);
});
