/**
 * <PERSON><PERSON><PERSON> to analyze and fix thumbnail URLs in the database
 */

const API_BASE_URL = 'https://www.bluefilmx.com/api';

async function analyzeThumbnailUrls() {
  console.log('🔍 Analyzing thumbnail URLs...');
  
  try {
    // Fetch videos to analyze URLs
    const response = await fetch(`${API_BASE_URL}/videos.php?limit=10`);
    
    if (!response.ok) {
      console.error('❌ Failed to fetch videos:', response.status);
      return;
    }
    
    const data = await response.json();
    const videos = data.data?.videos || [];
    
    console.log(`\n📊 Analyzing ${videos.length} videos...`);
    
    const urlAnalysis = {
      total: videos.length,
      hasSupabaseUrls: 0,
      hasNamecheapUrls: 0,
      hasEncodingIssues: 0,
      hasNoThumbnail: 0,
      examples: []
    };
    
    videos.forEach((video, index) => {
      const thumbnailUrl = video.thumbnail_url;
      const videoUrl = video.video_url;
      
      if (!thumbnailUrl) {
        urlAnalysis.hasNoThumbnail++;
        return;
      }
      
      // Check for encoding issues
      if (thumbnailUrl.includes('%0A') || thumbnailUrl.includes('\n') || thumbnailUrl.includes('\r')) {
        urlAnalysis.hasEncodingIssues++;
      }
      
      // Check URL types
      if (thumbnailUrl.includes('supabase.co')) {
        urlAnalysis.hasSupabaseUrls++;
      } else if (thumbnailUrl.includes('bluefilmx.com') || thumbnailUrl.includes('premium34.web-hosting.com')) {
        urlAnalysis.hasNamecheapUrls++;
      }
      
      // Collect examples
      if (index < 5) {
        urlAnalysis.examples.push({
          id: video.id,
          title: video.title?.substring(0, 30) + '...',
          thumbnailUrl: thumbnailUrl,
          videoUrl: videoUrl,
          hasEncodingIssue: thumbnailUrl.includes('%0A') || thumbnailUrl.includes('\n'),
          isSupabase: thumbnailUrl.includes('supabase.co'),
          isNamecheap: thumbnailUrl.includes('bluefilmx.com')
        });
      }
    });
    
    console.log('\n📈 URL Analysis Results:');
    console.log('Total videos:', urlAnalysis.total);
    console.log('Videos with Supabase URLs:', urlAnalysis.hasSupabaseUrls);
    console.log('Videos with Namecheap URLs:', urlAnalysis.hasNamecheapUrls);
    console.log('Videos with encoding issues:', urlAnalysis.hasEncodingIssues);
    console.log('Videos with no thumbnail:', urlAnalysis.hasNoThumbnail);
    
    console.log('\n🔍 Sample URLs:');
    urlAnalysis.examples.forEach((example, index) => {
      console.log(`\n${index + 1}. ${example.title}`);
      console.log('   Thumbnail:', example.thumbnailUrl);
      console.log('   Video:', example.videoUrl);
      console.log('   Issues:', {
        encodingIssue: example.hasEncodingIssue,
        isSupabase: example.isSupabase,
        isNamecheap: example.isNamecheap
      });
    });
    
    // Suggest fixes
    console.log('\n🔧 Suggested Fixes:');
    if (urlAnalysis.hasEncodingIssues > 0) {
      console.log('1. Clean up URL encoding issues (remove %0A, newlines)');
    }
    if (urlAnalysis.hasSupabaseUrls > 0) {
      console.log('2. Consider migrating Supabase URLs to Namecheap hosting');
    }
    if (urlAnalysis.hasNoThumbnail > 0) {
      console.log('3. Generate placeholder thumbnails for videos without thumbnails');
    }
    
    // Test a problematic URL
    if (urlAnalysis.examples.length > 0) {
      const firstExample = urlAnalysis.examples[0];
      if (firstExample.hasEncodingIssue) {
        console.log('\n🧪 Testing URL cleanup...');
        const originalUrl = firstExample.thumbnailUrl;
        const cleanedUrl = originalUrl.replace(/%0A/g, '').replace(/[\r\n\t]/g, '');
        
        console.log('Original URL:', originalUrl);
        console.log('Cleaned URL:', cleanedUrl);
        
        // Test if cleaned URL is accessible
        try {
          const testResponse = await fetch(cleanedUrl, { method: 'HEAD' });
          console.log('Cleaned URL status:', testResponse.status);
          if (testResponse.ok) {
            console.log('✅ Cleaned URL is accessible');
          } else {
            console.log('❌ Cleaned URL is not accessible');
          }
        } catch (error) {
          console.log('❌ Error testing cleaned URL:', error.message);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Error analyzing URLs:', error.message);
  }
}

// Run the analysis
analyzeThumbnailUrls();
