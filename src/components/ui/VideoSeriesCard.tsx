import React from 'react';
import { VideoSeries } from '../../utils/videoGrouping';
import { formatCount, formatDuration } from '../../utils/formatters';
import { Layers } from 'lucide-react';
import OptimizedImage from './OptimizedImage';

interface VideoSeriesCardProps {
  series: VideoSeries;
  onClick?: (videoId: string) => void;
  compact?: boolean;
}

const VideoSeriesCard: React.FC<VideoSeriesCardProps> = ({
  series,
  onClick,
  compact = false
}) => {
  // Use the first video as the representative for the series
  const firstVideo = series.videos[0];

  // Calculate total duration of all videos in the series
  const totalDuration = series.videos.reduce((total, video) => total + video.duration, 0);

  // Calculate total views of all videos in the series
  const totalViews = series.videos.reduce((total, video) => total + video.views, 0);

  const handleClick = () => {
    // When clicked, navigate to the first video in the series
    // The related videos will be shown on the video page
    if (onClick && firstVideo) {
      onClick(firstVideo.id);
    }
  };

  return (
    <div className={`${compact ? 'w-[280px]' : 'w-full'}`}>
      <div
        className="relative overflow-hidden cursor-pointer mb-4"
        onClick={handleClick}
      >
        <div className="relative aspect-video overflow-hidden bg-gray-900">
          <OptimizedImage
            src={firstVideo.thumbnailUrl || 'https://via.placeholder.com/640x360?text=No+Thumbnail'}
            alt={series.baseTitle}
            fallbackSrc="https://via.placeholder.com/640x360?text=No+Thumbnail"
            className="w-full h-full object-cover"
            width={640}
            height={360}
            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
          />

          {/* Duration badge */}
          {totalDuration > 0 && (
            <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded">
              {formatDuration(totalDuration)}
            </div>
          )}

          {/* Series badge */}
          <div className="absolute top-2 right-2 bg-blue-700 text-white text-xs px-2 py-1 rounded-sm font-medium flex items-center">
            <Layers size={12} className="mr-1" />
            {series.totalVideos} videos
          </div>
        </div>

        <div className="py-2">
          <h3 className="text-white font-medium line-clamp-2 text-sm">
            {series.baseTitle}
          </h3>
          <div className="flex items-center text-xs text-gray-400 mt-1">
            <span>{formatCount(totalViews)} Views</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoSeriesCard;
