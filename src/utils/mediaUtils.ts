/**
 * Utility functions for handling media (videos and images) in production
 */

/**
 * Check if a URL is accessible
 */
export const checkUrlAccessibility = async (url: string): Promise<boolean> => {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.error('URL accessibility check failed:', url, error);
    return false;
  }
};

/**
 * Validate and fix storage URLs (supports both Supabase and Namecheap storage)
 */
export const validateStorageUrl = (url: string): string => {
  if (!url) return '';

  // Clean up any whitespace and newline characters that might be in the URL
  const cleanedUrl = url.trim().replace(/[\r\n\t]/g, '').replace(/%0A/g, '');

  // Check if it's already a valid URL (Supabase or Namecheap)
  if (cleanedUrl.includes('supabase.co/storage/v1/object/public/') ||
      cleanedUrl.includes('bluefilmx.com/media/') ||
      cleanedUrl.startsWith('http://') ||
      cleanedUrl.startsWith('https://')) {
    return cleanedUrl;
  }

  // If it's a relative path for Supabase, construct the full URL
  if (cleanedUrl.startsWith('/storage/') || cleanedUrl.startsWith('storage/')) {
    const cleanPath = cleanedUrl.startsWith('/') ? cleanedUrl.slice(1) : cleanedUrl;
    return `https://vsnsglgyapexhwyfylic.supabase.co/${cleanPath}`;
  }

  // If it's a relative path for Namecheap media, construct the full URL
  if (cleanedUrl.startsWith('/media/') || cleanedUrl.startsWith('media/')) {
    const cleanPath = cleanedUrl.startsWith('/') ? cleanedUrl : `/${cleanedUrl}`;
    return `http://www.bluefilmx.com${cleanPath}`;
  }

  return cleanedUrl;
};

/**
 * Legacy function for backward compatibility
 */
export const validateSupabaseUrl = validateStorageUrl;

/**
 * Get a working thumbnail URL with fallbacks
 */
export const getWorkingThumbnailUrl = (thumbnailUrl: string): string => {
  // Validate and fix the URL first
  const validatedUrl = validateStorageUrl(thumbnailUrl);

  if (!validatedUrl) {
    return getPlaceholderThumbnail();
  }

  return validatedUrl;
};

/**
 * Get a working video URL with fallbacks
 */
export const getWorkingVideoUrl = (videoUrl: string): string => {
  // Validate and fix the URL first
  const validatedUrl = validateStorageUrl(videoUrl);

  if (!validatedUrl) {
    console.error('Invalid video URL:', videoUrl);
    return '';
  }

  return validatedUrl;
};

/**
 * Generate a placeholder thumbnail
 */
export const getPlaceholderThumbnail = (): string => {
  return `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='640' height='360' viewBox='0 0 640 360'%3E%3Crect width='640' height='360' fill='%23374151'/%3E%3Cg transform='translate(320,180)'%3E%3Ccircle cx='0' cy='0' r='30' fill='%236B7280'/%3E%3Cpolygon points='-10,-15 -10,15 20,0' fill='%23F3F4F6'/%3E%3C/g%3E%3Ctext x='320' y='320' font-family='Arial, sans-serif' font-size='18' fill='%239CA3AF' text-anchor='middle'%3ENo Thumbnail%3C/text%3E%3C/svg%3E`;
};

/**
 * Test if an image URL loads successfully
 */
export const testImageLoad = (url: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;

    // Timeout after 10 seconds
    setTimeout(() => resolve(false), 10000);
  });
};

/**
 * Test if a video URL loads successfully
 */
export const testVideoLoad = (url: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const video = document.createElement('video');
    video.onloadedmetadata = () => resolve(true);
    video.onerror = () => resolve(false);
    video.src = url;

    // Timeout after 15 seconds
    setTimeout(() => resolve(false), 15000);
  });
};

/**
 * Preload critical media assets with intelligent prioritization
 */
export const preloadMedia = async (videos: Array<{thumbnailUrl: string, videoUrl: string}>, options?: {
  priority?: 'high' | 'low';
  maxPreload?: number;
  includeNextPage?: boolean;
}) => {
  const { priority = 'high', maxPreload = 6, includeNextPage = false } = options || {};
  const videosToPreload = videos.slice(0, maxPreload);

  const preloadPromises = videosToPreload.map(async (video, index) => {
    if (video.thumbnailUrl) {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';

      // Use optimized URL with appropriate size
      const width = index < 3 ? 640 : 320; // First 3 get higher quality
      link.href = getOptimizedImageUrl(getWorkingThumbnailUrl(video.thumbnailUrl), width);

      // Set fetch priority
      if (priority === 'high' && index < 3) {
        link.setAttribute('fetchpriority', 'high');
      } else {
        link.setAttribute('fetchpriority', 'low');
      }

      document.head.appendChild(link);
    }
  });

  await Promise.allSettled(preloadPromises);
};

/**
 * Preload images in viewport with intersection observer
 */
export const preloadImagesInViewport = (imageUrls: string[], rootMargin = '200px') => {
  if (!('IntersectionObserver' in window)) {
    // Fallback: preload first few images
    imageUrls.slice(0, 3).forEach(url => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = getOptimizedImageUrl(url, 640);
      document.head.appendChild(link);
    });
    return;
  }

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          const src = img.dataset.src;
          if (src) {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = getOptimizedImageUrl(src, 640);
            document.head.appendChild(link);
          }
          observer.unobserve(img);
        }
      });
    },
    { rootMargin }
  );

  // Create placeholder elements to observe
  imageUrls.forEach(url => {
    const placeholder = document.createElement('div');
    placeholder.dataset.src = url;
    placeholder.style.height = '1px';
    placeholder.style.visibility = 'hidden';
    document.body.appendChild(placeholder);
    observer.observe(placeholder);
  });
};

/**
 * Fix common URL issues in production
 */
export const fixProductionUrls = (url: string): string => {
  if (!url) return '';

  // Remove any double slashes except after protocol
  const fixed = url.replace(/([^:]\/)\/+/g, '$1');

  // Ensure HTTPS in production
  if (fixed.startsWith('http://') && window.location.protocol === 'https:') {
    return fixed.replace('http://', 'https://');
  }

  return fixed;
};

/**
 * Get optimized image URL for different screen sizes with modern format support
 */
export const getOptimizedImageUrl = (url: string, width?: number, format?: 'webp' | 'avif' | 'auto'): string => {
  const validatedUrl = validateStorageUrl(url);

  if (!validatedUrl || !width) {
    return validatedUrl;
  }

  // For Supabase storage, we can add width parameters
  if (validatedUrl.includes('supabase.co/storage/v1/object/public/')) {
    try {
      const urlObj = new URL(validatedUrl);
      urlObj.searchParams.set('width', width.toString());

      // Optimize quality based on image size
      const quality = width <= 320 ? '75' : width <= 640 ? '80' : '85';
      urlObj.searchParams.set('quality', quality);

      // Add format optimization if supported
      if (format && format !== 'auto') {
        urlObj.searchParams.set('format', format);
      }

      return urlObj.toString();
    } catch (error) {
      console.error('Error optimizing image URL:', error);
      return validatedUrl;
    }
  }

  // For Namecheap storage, return the URL as-is (no optimization available)
  return validatedUrl;
};

/**
 * Generate multiple image sizes for responsive loading
 */
export const generateResponsiveImageSizes = (url: string): { [key: string]: string } => {
  const sizes = {
    small: getOptimizedImageUrl(url, 320),
    medium: getOptimizedImageUrl(url, 640),
    large: getOptimizedImageUrl(url, 1280),
    xlarge: getOptimizedImageUrl(url, 1920)
  };

  return sizes;
};

/**
 * Get the best image format based on browser support
 */
export const getBestImageFormat = (): 'webp' | 'avif' | 'auto' => {
  // Check for AVIF support
  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 1;

  try {
    // Check AVIF support
    if (canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0) {
      return 'avif';
    }
    // Check WebP support
    if (canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0) {
      return 'webp';
    }
  } catch (error) {
    // Fallback to auto
  }

  return 'auto';
};

/**
 * Generate low-quality image placeholder (LQIP)
 */
export const generateLQIP = (url: string): string => {
  return getOptimizedImageUrl(url, 40, 'auto'); // Very small, low quality for blur effect
};
