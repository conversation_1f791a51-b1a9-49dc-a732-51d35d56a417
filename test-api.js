/**
 * Simple test script to check API connectivity
 */

const API_BASE_URL = 'https://www.bluefilmx.com/api';

async function testAPI() {
  console.log('🔍 Testing API connectivity...');
  
  try {
    // Test 1: Basic connectivity
    console.log('\n1. Testing basic connectivity...');
    const response = await fetch(`${API_BASE_URL}/videos.php?limit=5`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error:', response.status, errorText);
      return;
    }
    
    const data = await response.json();
    console.log('✅ API Response received');
    console.log('Response structure:', {
      success: data.success,
      hasData: !!data.data,
      hasVideos: !!data.data?.videos,
      videoCount: data.data?.videos?.length || 0,
      hasPagination: !!data.data?.pagination
    });
    
    if (data.data?.videos?.length > 0) {
      const firstVideo = data.data.videos[0];
      console.log('First video sample:', {
        id: firstVideo.id,
        title: firstVideo.title?.substring(0, 50) + '...',
        thumbnail_url: firstVideo.thumbnail_url,
        video_url: firstVideo.video_url,
        hasUser: !!firstVideo.username
      });
    }
    
    // Test 2: Test single video endpoint
    if (data.data?.videos?.length > 0) {
      console.log('\n2. Testing single video endpoint...');
      const firstVideoId = data.data.videos[0].id;
      const singleResponse = await fetch(`${API_BASE_URL}/videos.php/${firstVideoId}`);
      
      if (singleResponse.ok) {
        const singleData = await singleResponse.json();
        console.log('✅ Single video endpoint working');
        console.log('Single video data:', {
          id: singleData.data?.id,
          title: singleData.data?.title?.substring(0, 50) + '...',
          views: singleData.data?.views
        });
      } else {
        console.error('❌ Single video endpoint failed:', singleResponse.status);
      }
    }
    
    // Test 3: Test categories endpoint
    console.log('\n3. Testing categories endpoint...');
    const categoriesResponse = await fetch(`${API_BASE_URL}/categories.php`);
    
    if (categoriesResponse.ok) {
      const categoriesData = await categoriesResponse.json();
      console.log('✅ Categories endpoint working');
      console.log('Categories count:', categoriesData.data?.length || 0);
    } else {
      console.error('❌ Categories endpoint failed:', categoriesResponse.status);
    }
    
  } catch (error) {
    console.error('❌ Network error:', error.message);
    
    // Additional debugging
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      console.log('\n🔧 Debugging suggestions:');
      console.log('- Check if the API server is running');
      console.log('- Verify CORS headers are properly set');
      console.log('- Check if the domain is accessible');
    }
  }
}

// Run the test
testAPI();
