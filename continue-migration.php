<?php
/**
 * Continue migrating remaining videos from Supabase to Namecheap storage
 */

// Database configuration
$host = 'localhost';
$dbname = 'bluerpcm_bluefilmx';
$username = 'bluerpcm_dbuser';
$password = 'kingpatrick100';

// Storage configuration
$namecheap_base_url = 'https://www.bluefilmx.com/media';
$media_directory = '/home/<USER>/public_html/media';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "🔄 CONTINUING VIDEO MIGRATION FROM SUPABASE TO NAMECHEAP\n";
    echo "======================================================\n\n";
    
    // 1. Check current migration status
    echo "1️⃣ Checking migration status...\n";
    
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN video_url LIKE '%supabase%' THEN 1 END) as supabase_count,
            COUNT(CASE WHEN video_url LIKE '%bluefilmx.com/media%' THEN 1 END) as namecheap_count
        FROM videos
    ");
    
    $status = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Migration Status:\n";
    echo "  Total videos: " . $status['total'] . "\n";
    echo "  On Supabase: " . $status['supabase_count'] . "\n";
    echo "  On Namecheap: " . $status['namecheap_count'] . "\n\n";
    
    if ($status['supabase_count'] == 0) {
        echo "🎉 ALL VIDEOS HAVE BEEN MIGRATED!\n";
        echo "No more videos need to be migrated.\n";
        exit(0);
    }
    
    // 2. Get next batch of videos to migrate
    echo "2️⃣ Getting next batch of videos to migrate...\n";
    
    $batch_size = 20; // Process 20 videos at a time
    
    $stmt = $pdo->prepare("
        SELECT id, title, video_url, thumbnail_url 
        FROM videos 
        WHERE video_url LIKE '%supabase%' 
        ORDER BY created_at DESC
        LIMIT ?
    ");
    
    $stmt->execute([$batch_size]);
    $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Found " . count($videos) . " videos to migrate in this batch\n\n";
    
    // 3. Migration process
    $migrated_count = 0;
    $failed_count = 0;
    
    foreach ($videos as $i => $video) {
        echo "📹 Migrating video " . ($i + 1) . "/" . count($videos) . ":\n";
        echo "  Title: " . substr($video['title'], 0, 50) . "...\n";
        echo "  ID: " . $video['id'] . "\n";
        
        $original_video_url = $video['video_url'];
        $original_thumbnail_url = $video['thumbnail_url'];
        
        // Extract filename from Supabase URL
        $video_filename = basename(parse_url($original_video_url, PHP_URL_PATH));
        $thumbnail_filename = basename(parse_url($original_thumbnail_url, PHP_URL_PATH));
        
        // Clean filenames (remove any problematic characters)
        $video_filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $video_filename);
        $thumbnail_filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $thumbnail_filename);
        
        // Ensure unique filenames
        $video_filename = $video['id'] . '_' . $video_filename;
        $thumbnail_filename = $video['id'] . '_' . $thumbnail_filename;
        
        $local_video_path = $media_directory . '/videos/' . $video_filename;
        $local_thumbnail_path = $media_directory . '/thumbnails/' . $thumbnail_filename;
        
        $new_video_url = $namecheap_base_url . '/videos/' . $video_filename;
        $new_thumbnail_url = $namecheap_base_url . '/thumbnails/' . $thumbnail_filename;
        
        $video_success = false;
        $thumbnail_success = false;
        
        // Download and save video file
        echo "  📥 Downloading video...\n";
        $video_content = @file_get_contents($original_video_url);
        if ($video_content !== false) {
            if (file_put_contents($local_video_path, $video_content)) {
                echo "  ✅ Video downloaded (" . number_format(strlen($video_content) / 1024 / 1024, 2) . " MB)\n";
                $video_success = true;
                chmod($local_video_path, 0644);
            } else {
                echo "  ❌ Failed to save video file\n";
            }
        } else {
            echo "  ❌ Failed to download video\n";
        }
        
        // Download and save thumbnail file
        echo "  📥 Downloading thumbnail...\n";
        $thumbnail_content = @file_get_contents($original_thumbnail_url);
        if ($thumbnail_content !== false) {
            if (file_put_contents($local_thumbnail_path, $thumbnail_content)) {
                echo "  ✅ Thumbnail downloaded (" . number_format(strlen($thumbnail_content) / 1024, 2) . " KB)\n";
                $thumbnail_success = true;
                chmod($local_thumbnail_path, 0644);
            } else {
                echo "  ❌ Failed to save thumbnail file\n";
            }
        } else {
            echo "  ❌ Failed to download thumbnail\n";
        }
        
        // Update database if both files were successful
        if ($video_success && $thumbnail_success) {
            $update_stmt = $pdo->prepare("
                UPDATE videos 
                SET video_url = ?, thumbnail_url = ? 
                WHERE id = ?
            ");
            
            if ($update_stmt->execute([$new_video_url, $new_thumbnail_url, $video['id']])) {
                echo "  ✅ Database updated successfully\n";
                $migrated_count++;
            } else {
                echo "  ❌ Failed to update database\n";
                $failed_count++;
            }
        } else {
            echo "  ❌ Migration failed - files not downloaded\n";
            $failed_count++;
        }
        
        echo "\n";
        
        // Add a small delay to avoid overwhelming the servers
        sleep(1);
    }
    
    // 4. Final status check
    echo "📋 BATCH MIGRATION SUMMARY:\n";
    echo "==========================\n";
    echo "✅ Successfully migrated: $migrated_count videos\n";
    echo "❌ Failed migrations: $failed_count videos\n\n";
    
    // Check overall migration status
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN video_url LIKE '%supabase%' THEN 1 END) as supabase_remaining,
            COUNT(CASE WHEN video_url LIKE '%bluefilmx.com/media%' THEN 1 END) as namecheap_total
        FROM videos
    ");
    
    $final_status = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "📊 OVERALL MIGRATION STATUS:\n";
    echo "============================\n";
    echo "Total videos: " . $final_status['total'] . "\n";
    echo "Migrated to Namecheap: " . $final_status['namecheap_total'] . "\n";
    echo "Remaining on Supabase: " . $final_status['supabase_remaining'] . "\n";
    
    $migration_percentage = round(($final_status['namecheap_total'] / $final_status['total']) * 100, 1);
    echo "Migration progress: " . $migration_percentage . "%\n\n";
    
    if ($final_status['supabase_remaining'] > 0) {
        echo "🔄 CONTINUE MIGRATION:\n";
        echo "Run this script again to migrate the remaining " . $final_status['supabase_remaining'] . " videos.\n";
        echo "Command: php continue-migration.php\n\n";
    } else {
        echo "🎉 MIGRATION COMPLETE!\n";
        echo "All videos have been successfully migrated to Namecheap storage.\n\n";
    }
    
    echo "🌐 Your videos are now being served from: $namecheap_base_url\n";
    echo "🎬 Test your website: https://www.bluefilmx.com\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
